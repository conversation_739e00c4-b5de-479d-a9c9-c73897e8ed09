# Caddyfile 配置文件

buoy.hanjie-tech.cn {
    # API 反向代理到后端服务
    handle /api/* {
        reverse_proxy backend:8000
    }

    # WebSocket 支持
    handle /ws/* {
        reverse_proxy backend:8000
    }

    # 前端反向代理到 Vite 开发服务器
    handle /* {
        reverse_proxy frontend:3000 {
            # 支持 WebSocket 连接（Vite HMR）
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # 启用 gzip 压缩
    encode gzip

    # 设置安全头（开发环境相对宽松）
    header {
        # 防止点击劫持
        X-Frame-Options "SAMEORIGIN"
        # 防止 MIME 类型嗅探
        X-Content-Type-Options "nosniff"
        # XSS 保护
        X-XSS-Protection "1; mode=block"
        # 引用策略
        Referrer-Policy "strict-origin-when-cross-origin"
    }

    # 日志记录
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# 可选：HTTP 到 HTTPS 重定向（Caddy 自动处理）
# http://buoy.hanjie-tech.cn {
#     redir https://buoy.hanjie-tech.cn{uri} permanent
# }
